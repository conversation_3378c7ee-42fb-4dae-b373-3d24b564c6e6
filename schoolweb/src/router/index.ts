import type { App } from 'vue';
import {
  type RouterHistory,
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory
} from 'vue-router';
import { createBuiltinVueRoutes } from './routes/builtin';
import { createRouterGuard } from './guard';
import IdentityVerify from '@/views/profile/identity-verify.vue';
import BaseLayout from '@/layouts/base-layout/index.vue';
import AdminAudit from '@/views/admin/audit.vue';
import SocialPost from '@/views/social/post.vue';

const { VITE_ROUTER_HISTORY_MODE = 'history', VITE_BASE_URL } = import.meta.env;

const historyCreatorMap: Record<Env.RouterHistoryMode, (base?: string) => RouterHistory> = {
  hash: createWebHashHistory,
  history: createWebHistory,
  memory: createMemoryHistory
};

export const router = createRouter({
  history: historyCreatorMap[VITE_ROUTER_HISTORY_MODE](VITE_BASE_URL),
  routes: [
    ...createBuiltinVueRoutes(),
    {
      path: '/profile/identity',
      component: BaseLayout,
      children: [
        {
          path: '',
          component: IdentityVerify,
          meta: {
            title: '身份认证'
          }
        }
      ]
    },
    {
      path: '/admin/audit',
      component: BaseLayout,
      children: [
        {
          path: '',
          name: 'audit-management',
          component: AdminAudit,
          meta: {
            title: '审核管理',
            icon: 'mdi:clipboard-check',
            order: 2,
            roles: ['superAdmin'],
            keepAlive: true
          }
        }
      ]
    },
    {
      path: '/social/post/:id',
      name: 'social-post',
      component: BaseLayout,
      meta: {
        title: '动态详情',
        hideInMenu: true,
        roles: ['student', 'teacher', 'superAdmin']
      },
      children: [
        {
          path: '',
          name: 'social-post-detail',
          component: SocialPost,
          props: route => ({ id: route.params.id })
        }
      ]
    }
  ]
});

/** Setup Vue Router */
export async function setupRouter(app: App) {
  app.use(router);
  createRouterGuard(router);
  await router.isReady();
}
