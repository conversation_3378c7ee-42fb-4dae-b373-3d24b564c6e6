# 校园技能共享平台技术难点解析文档

## 1. 分布式事务处理（订单与余额变更）

### 1.1 问题描述

在订单支付场景中，需要同时完成以下操作：
1. 冻结买家钱包余额（支付时）
2. 扣除冻结金额并转账给卖家（订单完成时）
3. 更新订单状态
4. 记录交易流水

这些操作必须保证原子性，要么全部成功，要么全部失败。

### 1.2 项目实际实现方案

#### 1.2.1 订单支付流程（本地事务 + 乐观锁）

项目采用分阶段支付模式，使用Spring的`@Transactional`注解和数据库乐观锁：

```java
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private WalletService walletService;

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 订单支付处理（实际项目实现）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payOrder(Long orderId, Integer userId) {
        // 1. 获取订单信息
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        // 2. 检查权限
        if (!order.getBuyerId().equals(userId)) {
            throw new RuntimeException("无权限操作该订单");
        }

        // 3. 检查订单状态
        if (order.getStatus() != Order.OrderStatus.PENDING_PAYMENT.getCode()) {
            throw new RuntimeException("订单状态不正确");
        }

        // 4. 冻结余额（相当于支付）
        boolean freezeSuccess = walletService.freezeBalance(
                userId, order.getTotalAmount(), orderId, "订单支付：" + order.getTitle());

        if (!freezeSuccess) {
            throw new RuntimeException("支付失败，余额不足");
        }

        // 5. 更新订单状态
        int updateResult = orderMapper.payOrder(
                orderId, Order.OrderStatus.PENDING_ACCEPT.getCode(), new Date(), 0);

        if (updateResult == 0) {
            // 支付失败，解冻余额
            walletService.unfreezeBalance(userId, order.getTotalAmount(), orderId, "订单支付失败解冻");
            throw new RuntimeException("订单支付失败，请重试");
        }

        log.info("订单支付成功，订单号：{}，用户：{}，金额：{}",
                order.getOrderNo(), userId, order.getTotalAmount());

        return true;
    }
}
```

#### 1.2.2 钱包服务实现（乐观锁）

```java
@Service
public class WalletServiceImpl implements WalletService {
    
    /**
     * 扣减余额（使用乐观锁防止并发问题）
     */
    @Override
    public boolean deductBalance(Integer userId, BigDecimal amount) {
        for (int i = 0; i < 3; i++) { // 最多重试3次
            Wallet wallet = getByUserId(userId);
            
            // 检查余额
            if (wallet.getBalance().compareTo(amount) < 0) {
                throw new BusinessException("余额不足");
            }
            
            // 计算新余额
            BigDecimal newBalance = wallet.getBalance().subtract(amount);
            
            // 使用乐观锁更新
            LambdaUpdateWrapper<Wallet> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Wallet::getUserId, userId)
                        .eq(Wallet::getVersion, wallet.getVersion()) // 乐观锁条件
                        .set(Wallet::getBalance, newBalance)
                        .set(Wallet::getVersion, wallet.getVersion() + 1)
                        .set(Wallet::getUpdatedTime, new Date());
            
            boolean updateResult = update(updateWrapper);
            if (updateResult) {
                return true; // 更新成功
            }
            
            // 更新失败，等待后重试
            try {
                Thread.sleep(50 + i * 10); // 递增等待时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        throw new BusinessException("余额扣减失败，请重试");
    }
}
```

### 1.3 分布式事务扩展方案

#### 1.3.1 TCC模式（Try-Confirm-Cancel）

```java
@Component
public class OrderPaymentTccService {
    
    /**
     * Try阶段：预留资源
     */
    public boolean tryPayment(Long orderId, Integer buyerId, BigDecimal amount) {
        // 1. 冻结买家余额
        return walletService.freezeBalance(buyerId, amount);
    }
    
    /**
     * Confirm阶段：确认执行
     */
    public boolean confirmPayment(Long orderId, Integer buyerId, Integer sellerId, BigDecimal amount) {
        try {
            // 1. 扣减买家冻结余额
            walletService.deductFrozenBalance(buyerId, amount);
            
            // 2. 增加卖家余额
            walletService.addBalance(sellerId, amount);
            
            // 3. 更新订单状态
            orderService.updateStatusToPaid(orderId);
            
            return true;
        } catch (Exception e) {
            // 确认失败，需要补偿
            return false;
        }
    }
    
    /**
     * Cancel阶段：取消执行
     */
    public boolean cancelPayment(Long orderId, Integer buyerId, BigDecimal amount) {
        // 解冻买家余额
        return walletService.unfreezeBalance(buyerId, amount);
    }
}
```

#### 1.3.2 Saga模式（补偿事务）

```java
@Component
public class OrderPaymentSagaService {
    
    /**
     * 正向操作链
     */
    public void executePaymentSaga(PaymentContext context) {
        try {
            // 步骤1：扣减买家余额
            walletService.deductBalance(context.getBuyerId(), context.getAmount());
            context.addCompensation(() -> walletService.addBalance(context.getBuyerId(), context.getAmount()));
            
            // 步骤2：增加卖家余额
            walletService.addBalance(context.getSellerId(), context.getAmount());
            context.addCompensation(() -> walletService.deductBalance(context.getSellerId(), context.getAmount()));
            
            // 步骤3：更新订单状态
            orderService.updateStatusToPaid(context.getOrderId());
            context.addCompensation(() -> orderService.updateStatusToPending(context.getOrderId()));
            
            // 步骤4：记录交易流水
            transactionService.recordPayment(context);
            context.addCompensation(() -> transactionService.cancelPayment(context));
            
        } catch (Exception e) {
            // 执行补偿操作
            context.executeCompensations();
            throw new BusinessException("支付失败");
        }
    }
}
```

## 2. 并发控制（防止超卖、重复预约）

### 2.1 问题描述

在技能服务预约场景中，可能出现以下并发问题：
1. 超卖：同一时间段被多人预约
2. 重复预约：同一用户重复提交预约
3. 库存不一致：并发修改导致数据不一致

### 2.2 技术方案

#### 2.2.1 数据库层面控制

**唯一索引防重复：**
```sql
-- 防止重复预约的唯一索引
ALTER TABLE orders ADD UNIQUE INDEX uk_buyer_service_time (buyer_id, service_id, appointment_time);

-- 防止超卖的库存字段
ALTER TABLE skill_services ADD COLUMN available_slots INT DEFAULT 1 COMMENT '可预约名额';
```

**乐观锁控制库存：**
```java
@Service
public class SkillServiceBookingService {
    
    /**
     * 预约技能服务
     */
    @Transactional(rollbackFor = Exception.class)
    public Long bookService(BookingRequest request) {
        // 1. 检查重复预约
        if (hasExistingBooking(request.getBuyerId(), request.getServiceId(), request.getAppointmentTime())) {
            throw new BusinessException("您已预约过该时间段");
        }
        
        // 2. 使用乐观锁扣减库存
        boolean stockDeducted = deductAvailableSlots(request.getServiceId(), 1);
        if (!stockDeducted) {
            throw new BusinessException("预约失败，名额已满");
        }
        
        // 3. 创建订单
        Order order = createOrder(request);
        orderService.save(order);
        
        return order.getOrderId();
    }
    
    /**
     * 使用乐观锁扣减可用名额
     */
    private boolean deductAvailableSlots(Long serviceId, int count) {
        for (int i = 0; i < 3; i++) { // 最多重试3次
            SkillService service = skillServiceService.getById(serviceId);
            
            if (service.getAvailableSlots() < count) {
                throw new BusinessException("可预约名额不足");
            }
            
            // 使用乐观锁更新
            LambdaUpdateWrapper<SkillService> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SkillService::getServiceId, serviceId)
                        .eq(SkillService::getVersion, service.getVersion())
                        .set(SkillService::getAvailableSlots, service.getAvailableSlots() - count)
                        .set(SkillService::getVersion, service.getVersion() + 1);
            
            boolean updated = skillServiceService.update(updateWrapper);
            if (updated) {
                return true;
            }
            
            // 更新失败，短暂等待后重试
            try {
                Thread.sleep(10 + i * 5);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        return false;
    }
}
```

#### 2.2.2 Redis分布式锁

```java
@Component
public class RedisDistributedLock {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    private static final String LOCK_PREFIX = "lock:";
    private static final int DEFAULT_EXPIRE_TIME = 30; // 30秒
    
    /**
     * 获取分布式锁
     */
    public boolean tryLock(String key, String value, int expireTime) {
        String lockKey = LOCK_PREFIX + key;
        Boolean result = redisTemplate.opsForValue().setIfAbsent(lockKey, value, expireTime, TimeUnit.SECONDS);
        return Boolean.TRUE.equals(result);
    }
    
    /**
     * 释放分布式锁
     */
    public boolean releaseLock(String key, String value) {
        String lockKey = LOCK_PREFIX + key;
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Long result = redisTemplate.execute(new DefaultRedisScript<>(script, Long.class), 
                                          Collections.singletonList(lockKey), value);
        return Long.valueOf(1).equals(result);
    }
}

@Service
public class BookingServiceWithLock {
    
    @Autowired
    private RedisDistributedLock distributedLock;
    
    /**
     * 使用分布式锁的预约服务
     */
    public Long bookServiceWithLock(BookingRequest request) {
        String lockKey = "booking:" + request.getServiceId() + ":" + request.getAppointmentTime();
        String lockValue = UUID.randomUUID().toString();
        
        // 尝试获取锁
        if (!distributedLock.tryLock(lockKey, lockValue, 10)) {
            throw new BusinessException("系统繁忙，请稍后重试");
        }
        
        try {
            // 执行预约逻辑
            return bookService(request);
        } finally {
            // 释放锁
            distributedLock.releaseLock(lockKey, lockValue);
        }
    }
}
```

#### 2.2.3 防重复提交

**前端防重复：**
```typescript
// 防重复提交Hook
export function usePreventDuplicate() {
  const submitting = ref(false);
  
  const submit = async (fn: () => Promise<any>) => {
    if (submitting.value) {
      return Promise.reject(new Error('请勿重复提交'));
    }
    
    submitting.value = true;
    try {
      return await fn();
    } finally {
      submitting.value = false;
    }
  };
  
  return { submitting: readonly(submitting), submit };
}
```

**后端幂等性控制：**
```java
@Component
public class IdempotentService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    private static final String IDEMPOTENT_PREFIX = "idempotent:";
    
    /**
     * 检查并设置幂等性标识
     */
    public boolean checkAndSetIdempotent(String key, int expireSeconds) {
        String idempotentKey = IDEMPOTENT_PREFIX + key;
        Boolean result = redisTemplate.opsForValue().setIfAbsent(idempotentKey, "1", expireSeconds, TimeUnit.SECONDS);
        return Boolean.TRUE.equals(result);
    }
    
    /**
     * 清除幂等性标识
     */
    public void clearIdempotent(String key) {
        String idempotentKey = IDEMPOTENT_PREFIX + key;
        redisTemplate.delete(idempotentKey);
    }
}

@RestController
public class BookingController {
    
    @Autowired
    private IdempotentService idempotentService;
    
    @PostMapping("/booking")
    public SaResult createBooking(@RequestBody BookingRequest request, HttpServletRequest httpRequest) {
        // 生成幂等性key（用户ID + 请求参数hash）
        String idempotentKey = generateIdempotentKey(request, httpRequest);
        
        // 检查幂等性
        if (!idempotentService.checkAndSetIdempotent(idempotentKey, 60)) {
            return SaResult.error("请勿重复提交");
        }
        
        try {
            Long orderId = bookingService.bookService(request);
            return SaResult.ok("预约成功").setData(orderId);
        } catch (Exception e) {
            // 业务异常时清除幂等性标识，允许重试
            idempotentService.clearIdempotent(idempotentKey);
            throw e;
        }
    }
    
    private String idempotentKey(BookingRequest request, HttpServletRequest httpRequest) {
        String userId = StpUtil.getLoginIdAsString();
        String requestHash = DigestUtils.md5Hex(JSON.toJSONString(request));
        return userId + ":" + requestHash;
    }
}
```

## 3. 安全防护（SQL注入、XSS攻击）

### 3.1 SQL注入防护

#### 3.1.1 问题描述

SQL注入是通过在输入参数中插入恶意SQL代码，从而控制数据库执行非预期操作的攻击方式。

#### 3.1.2 防护方案

**使用MyBatis Plus参数绑定：**
```java
// 错误示例：直接拼接SQL（存在注入风险）
@Select("SELECT * FROM users WHERE name = '" + name + "'")
List<Users> findByNameUnsafe(String name);

// 正确示例：使用参数绑定
@Select("SELECT * FROM users WHERE name = #{name}")
List<Users> findByNameSafe(@Param("name") String name);

// 使用LambdaQueryWrapper（推荐）
public List<Users> findByName(String name) {
    LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(Users::getName, name);
    return userMapper.selectList(queryWrapper);
}
```

**动态SQL安全处理：**
```xml
<!-- MyBatis XML映射文件 -->
<select id="searchUsers" resultType="com.school.entity.Users">
    SELECT * FROM users
    <where>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="userType != null">
            AND user_type = #{userType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
    </where>
    ORDER BY
    <choose>
        <when test="orderBy == 'name'">name</when>
        <when test="orderBy == 'created_time'">created_time</when>
        <otherwise>user_id</otherwise>
    </choose>
    <if test="orderDirection == 'desc'">DESC</if>
</select>
```

**输入参数验证：**
```java
@RestController
@Validated
public class UserController {

    @GetMapping("/search")
    public SaResult searchUsers(
            @RequestParam @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]{1,50}$", message = "用户名格式不正确") String name,
            @RequestParam @Min(1) @Max(2) Integer userType,
            @RequestParam @Pattern(regexp = "^(name|created_time)$", message = "排序字段不合法") String orderBy) {

        // 业务逻辑
        return userService.searchUsers(name, userType, orderBy);
    }
}

@Component
public class SqlInjectionValidator {

    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript|onload|onerror)"
    );

    /**
     * 检查SQL注入风险
     */
    public boolean containsSqlInjection(String input) {
        if (StringUtils.isEmpty(input)) {
            return false;
        }
        return SQL_INJECTION_PATTERN.matcher(input).find();
    }

    /**
     * 清理危险字符
     */
    public String sanitizeInput(String input) {
        if (StringUtils.isEmpty(input)) {
            return input;
        }

        // 移除危险字符
        return input.replaceAll("[';\"\\\\]", "")
                   .replaceAll("(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute)", "");
    }
}
```

### 3.2 XSS攻击防护

#### 3.2.1 问题描述

XSS（跨站脚本攻击）是通过在网页中注入恶意脚本代码，当用户浏览网页时执行这些脚本，从而获取用户信息或执行恶意操作。

#### 3.2.2 防护方案

**后端输出编码：**
```java
@Component
public class XssFilter {

    /**
     * HTML编码
     */
    public String encodeHtml(String input) {
        if (StringUtils.isEmpty(input)) {
            return input;
        }

        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;")
                   .replace("/", "&#x2F;");
    }

    /**
     * JavaScript编码
     */
    public String encodeJavaScript(String input) {
        if (StringUtils.isEmpty(input)) {
            return input;
        }

        StringBuilder sb = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (c < 256) {
                String hex = Integer.toHexString(c);
                if (hex.length() == 1) {
                    hex = "0" + hex;
                }
                sb.append("\\x").append(hex);
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 清理HTML标签
     */
    public String stripHtml(String input) {
        if (StringUtils.isEmpty(input)) {
            return input;
        }

        // 使用Jsoup清理HTML
        return Jsoup.clean(input, Whitelist.none());
    }
}

@RestController
public class PostController {

    @Autowired
    private XssFilter xssFilter;

    @PostMapping("/posts")
    public SaResult createPost(@RequestBody @Valid PostRequest request) {
        // 清理用户输入
        String cleanContent = xssFilter.stripHtml(request.getContent());
        request.setContent(cleanContent);

        // 创建帖子
        Long postId = postService.createPost(request);
        return SaResult.ok("发布成功").setData(postId);
    }
}
```

**前端输入过滤：**
```typescript
// XSS防护工具类
export class XssProtection {

  // HTML编码
  static encodeHtml(str: string): string {
    if (!str) return str;

    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
  }

  // 清理HTML标签
  static stripHtml(str: string): string {
    if (!str) return str;

    const div = document.createElement('div');
    div.innerHTML = str;
    return div.textContent || div.innerText || '';
  }

  // 验证URL安全性
  static isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  }

  // 清理危险属性
  static sanitizeAttributes(element: HTMLElement): void {
    const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus'];
    dangerousAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        element.removeAttribute(attr);
      }
    });
  }
}

// Vue组件中使用
<template>
  <div>
    <!-- 安全显示用户输入内容 -->
    <div v-html="sanitizedContent"></div>

    <!-- 表单输入验证 -->
    <el-input
      v-model="userInput"
      @input="validateInput"
      :maxlength="500"
      placeholder="请输入内容"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { XssProtection } from '@/utils/xssProtection';

const userInput = ref('');

// 清理后的内容
const sanitizedContent = computed(() => {
  return XssProtection.encodeHtml(userInput.value);
});

// 输入验证
const validateInput = (value: string) => {
  // 检查是否包含脚本标签
  const scriptPattern = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
  if (scriptPattern.test(value)) {
    ElMessage.warning('输入内容包含不安全字符');
    userInput.value = XssProtection.stripHtml(value);
  }
};
</script>
```

**Content Security Policy (CSP)配置：**
```java
@Configuration
public class SecurityConfig {

    @Bean
    public FilterRegistrationBean<CspFilter> cspFilter() {
        FilterRegistrationBean<CspFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new CspFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(1);
        return registration;
    }
}

public class CspFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 设置CSP头
        httpResponse.setHeader("Content-Security-Policy",
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
            "style-src 'self' 'unsafe-inline'; " +
            "img-src 'self' data: blob:; " +
            "font-src 'self'; " +
            "connect-src 'self'; " +
            "frame-ancestors 'none';"
        );

        // 其他安全头
        httpResponse.setHeader("X-Content-Type-Options", "nosniff");
        httpResponse.setHeader("X-Frame-Options", "DENY");
        httpResponse.setHeader("X-XSS-Protection", "1; mode=block");

        chain.doFilter(request, response);
    }
}
```

## 4. 基于用户行为的推荐系统

### 4.1 问题描述

为用户推荐感兴趣的技能服务，需要基于用户的历史行为（浏览、收藏、购买、评价等）进行个性化推荐。

### 4.2 技术方案

#### 4.2.1 用户行为数据收集

```java
@Entity
@Table(name = "user_behaviors")
public class UserBehavior {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Integer userId;
    private Long targetId;
    private String targetType; // skill_service, post, user
    private String behaviorType; // view, like, collect, purchase, comment
    private String behaviorData; // JSON格式的行为详情
    private Date behaviorTime;
    private String sessionId;
    private String userAgent;
    private String ipAddress;
}

@Service
public class UserBehaviorService {

    /**
     * 记录用户行为
     */
    @Async
    public void recordBehavior(Integer userId, Long targetId, String targetType,
                              String behaviorType, Map<String, Object> behaviorData) {
        UserBehavior behavior = new UserBehavior();
        behavior.setUserId(userId);
        behavior.setTargetId(targetId);
        behavior.setTargetType(targetType);
        behavior.setBehaviorType(behaviorType);
        behavior.setBehaviorData(JSON.toJSONString(behaviorData));
        behavior.setBehaviorTime(new Date());

        userBehaviorMapper.insert(behavior);

        // 异步更新用户画像
        updateUserProfile(userId, targetId, targetType, behaviorType);
    }

    /**
     * 更新用户画像
     */
    private void updateUserProfile(Integer userId, Long targetId, String targetType, String behaviorType) {
        // 获取目标对象的标签和分类
        if ("skill_service".equals(targetType)) {
            SkillService service = skillServiceService.getById(targetId);
            if (service != null) {
                // 更新用户对该分类的兴趣度
                updateCategoryInterest(userId, service.getCategoryId(), behaviorType);

                // 更新用户对标签的兴趣度
                updateTagInterests(userId, service.getTags(), behaviorType);
            }
        }
    }
}
```

#### 4.2.2 协同过滤推荐算法

```java
@Service
public class CollaborativeFilteringService {

    /**
     * 基于用户的协同过滤
     */
    public List<Long> recommendByUserSimilarity(Integer userId, int limit) {
        // 1. 找到相似用户
        List<Integer> similarUsers = findSimilarUsers(userId, 50);

        // 2. 获取相似用户喜欢的技能服务
        Map<Long, Double> serviceScores = new HashMap<>();

        for (Integer similarUserId : similarUsers) {
            List<UserBehavior> behaviors = getUserPositiveBehaviors(similarUserId);
            double userSimilarity = calculateUserSimilarity(userId, similarUserId);

            for (UserBehavior behavior : behaviors) {
                if ("skill_service".equals(behavior.getTargetType())) {
                    Long serviceId = behavior.getTargetId();
                    double score = getBehaviorScore(behavior.getBehaviorType()) * userSimilarity;
                    serviceScores.merge(serviceId, score, Double::sum);
                }
            }
        }

        // 3. 过滤用户已经交互过的服务
        Set<Long> userInteractedServices = getUserInteractedServices(userId);
        serviceScores.keySet().removeAll(userInteractedServices);

        // 4. 按分数排序并返回
        return serviceScores.entrySet().stream()
                .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
                .limit(limit)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 计算用户相似度（余弦相似度）
     */
    private double calculateUserSimilarity(Integer userId1, Integer userId2) {
        Map<Long, Double> user1Vector = getUserVector(userId1);
        Map<Long, Double> user2Vector = getUserVector(userId2);

        // 计算共同评价的物品
        Set<Long> commonItems = new HashSet<>(user1Vector.keySet());
        commonItems.retainAll(user2Vector.keySet());

        if (commonItems.isEmpty()) {
            return 0.0;
        }

        // 计算余弦相似度
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (Long itemId : commonItems) {
            double rating1 = user1Vector.get(itemId);
            double rating2 = user2Vector.get(itemId);

            dotProduct += rating1 * rating2;
            norm1 += rating1 * rating1;
            norm2 += rating2 * rating2;
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 获取用户向量（物品ID -> 评分）
     */
    private Map<Long, Double> getUserVector(Integer userId) {
        List<UserBehavior> behaviors = getUserBehaviors(userId);
        Map<Long, Double> vector = new HashMap<>();

        for (UserBehavior behavior : behaviors) {
            if ("skill_service".equals(behavior.getTargetType())) {
                Long serviceId = behavior.getTargetId();
                double score = getBehaviorScore(behavior.getBehaviorType());
                vector.merge(serviceId, score, Double::sum);
            }
        }

        return vector;
    }

    /**
     * 获取行为分数
     */
    private double getBehaviorScore(String behaviorType) {
        switch (behaviorType) {
            case "view": return 1.0;
            case "like": return 2.0;
            case "collect": return 3.0;
            case "purchase": return 5.0;
            case "comment": return 2.5;
            case "rating": return 4.0;
            default: return 1.0;
        }
    }
}
```

#### 4.2.3 基于内容的推荐算法

```java
@Service
public class ContentBasedRecommendationService {

    /**
     * 基于内容的推荐
     */
    public List<Long> recommendByContent(Integer userId, int limit) {
        // 1. 获取用户画像
        UserProfile userProfile = getUserProfile(userId);

        // 2. 获取所有可推荐的技能服务
        List<SkillService> availableServices = getAvailableServices(userId);

        // 3. 计算内容相似度分数
        Map<Long, Double> serviceScores = new HashMap<>();

        for (SkillService service : availableServices) {
            double score = calculateContentSimilarity(userProfile, service);
            serviceScores.put(service.getServiceId(), score);
        }

        // 4. 按分数排序并返回
        return serviceScores.entrySet().stream()
                .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
                .limit(limit)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 计算内容相似度
     */
    private double calculateContentSimilarity(UserProfile userProfile, SkillService service) {
        double score = 0.0;

        // 1. 分类匹配度
        Integer categoryId = service.getCategoryId();
        Double categoryInterest = userProfile.getCategoryInterests().get(categoryId);
        if (categoryInterest != null) {
            score += categoryInterest * 0.4; // 分类权重40%
        }

        // 2. 标签匹配度
        List<String> serviceTags = parseServiceTags(service.getTags());
        double tagScore = 0.0;
        int matchedTags = 0;

        for (String tag : serviceTags) {
            Double tagInterest = userProfile.getTagInterests().get(tag);
            if (tagInterest != null) {
                tagScore += tagInterest;
                matchedTags++;
            }
        }

        if (matchedTags > 0) {
            score += (tagScore / matchedTags) * 0.3; // 标签权重30%
        }

        // 3. 价格偏好匹配度
        double priceScore = calculatePricePreference(userProfile, service.getPrice());
        score += priceScore * 0.2; // 价格权重20%

        // 4. 服务质量分数
        double qualityScore = calculateQualityScore(service);
        score += qualityScore * 0.1; // 质量权重10%

        return score;
    }

    /**
     * 计算价格偏好匹配度
     */
    private double calculatePricePreference(UserProfile userProfile, BigDecimal servicePrice) {
        BigDecimal avgPrice = userProfile.getAveragePrice();
        BigDecimal priceStd = userProfile.getPriceStandardDeviation();

        if (avgPrice == null || priceStd == null) {
            return 0.5; // 默认中等偏好
        }

        // 使用正态分布计算价格偏好
        double diff = servicePrice.subtract(avgPrice).doubleValue();
        double variance = priceStd.doubleValue() * priceStd.doubleValue();

        return Math.exp(-0.5 * (diff * diff) / variance);
    }

    /**
     * 计算服务质量分数
     */
    private double calculateQualityScore(SkillService service) {
        double ratingScore = service.getRatingAvg().doubleValue() / 5.0; // 评分归一化
        double popularityScore = Math.log(service.getOrderCount() + 1) / Math.log(100); // 订单数归一化

        return (ratingScore * 0.7 + popularityScore * 0.3);
    }
}
```

#### 4.2.4 混合推荐算法

```java
@Service
public class HybridRecommendationService {

    @Autowired
    private CollaborativeFilteringService collaborativeFilteringService;

    @Autowired
    private ContentBasedRecommendationService contentBasedService;

    @Autowired
    private PopularityBasedRecommendationService popularityService;

    /**
     * 混合推荐算法
     */
    public List<Long> hybridRecommend(Integer userId, int limit) {
        Map<Long, Double> finalScores = new HashMap<>();

        // 1. 协同过滤推荐（权重40%）
        try {
            List<Long> cfRecommendations = collaborativeFilteringService.recommendByUserSimilarity(userId, limit * 2);
            addScoresWithWeight(finalScores, cfRecommendations, 0.4, "collaborative");
        } catch (Exception e) {
            log.warn("协同过滤推荐失败", e);
        }

        // 2. 基于内容推荐（权重35%）
        try {
            List<Long> contentRecommendations = contentBasedService.recommendByContent(userId, limit * 2);
            addScoresWithWeight(finalScores, contentRecommendations, 0.35, "content");
        } catch (Exception e) {
            log.warn("基于内容推荐失败", e);
        }

        // 3. 热门推荐（权重25%）
        try {
            List<Long> popularRecommendations = popularityService.getPopularServices(limit);
            addScoresWithWeight(finalScores, popularRecommendations, 0.25, "popularity");
        } catch (Exception e) {
            log.warn("热门推荐失败", e);
        }

        // 4. 多样性调整
        List<Long> diversifiedResults = diversifyRecommendations(finalScores, userId, limit);

        return diversifiedResults;
    }

    /**
     * 添加加权分数
     */
    private void addScoresWithWeight(Map<Long, Double> finalScores, List<Long> recommendations,
                                   double weight, String source) {
        for (int i = 0; i < recommendations.size(); i++) {
            Long serviceId = recommendations.get(i);
            // 位置越靠前分数越高
            double positionScore = (recommendations.size() - i) / (double) recommendations.size();
            double weightedScore = positionScore * weight;

            finalScores.merge(serviceId, weightedScore, Double::sum);
        }
    }

    /**
     * 多样性调整
     */
    private List<Long> diversifyRecommendations(Map<Long, Double> scores, Integer userId, int limit) {
        List<Long> result = new ArrayList<>();
        Set<Integer> selectedCategories = new HashSet<>();

        // 按分数排序
        List<Map.Entry<Long, Double>> sortedEntries = scores.entrySet().stream()
                .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
                .collect(Collectors.toList());

        // 多样性选择
        for (Map.Entry<Long, Double> entry : sortedEntries) {
            if (result.size() >= limit) {
                break;
            }

            Long serviceId = entry.getKey();
            SkillService service = skillServiceService.getById(serviceId);

            if (service != null) {
                // 限制同一分类的推荐数量
                if (selectedCategories.size() < 3 || !selectedCategories.contains(service.getCategoryId())) {
                    result.add(serviceId);
                    selectedCategories.add(service.getCategoryId());
                }
            }
        }

        // 如果结果不够，补充高分项目
        if (result.size() < limit) {
            for (Map.Entry<Long, Double> entry : sortedEntries) {
                if (result.size() >= limit) {
                    break;
                }

                Long serviceId = entry.getKey();
                if (!result.contains(serviceId)) {
                    result.add(serviceId);
                }
            }
        }

        return result;
    }
}
```

#### 4.2.5 实时推荐接口

```java
@RestController
@RequestMapping("/api/recommendation")
public class RecommendationController {

    @Autowired
    private HybridRecommendationService hybridRecommendationService;

    @Autowired
    private UserBehaviorService userBehaviorService;

    /**
     * 获取个性化推荐
     */
    @GetMapping("/personalized")
    @SaCheckLogin
    public SaResult getPersonalizedRecommendations(
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(defaultValue = "hybrid") String algorithm) {

        Integer userId = StpUtil.getLoginIdAsInt();

        // 记录推荐请求行为
        userBehaviorService.recordBehavior(userId, null, "recommendation", "request",
                Map.of("algorithm", algorithm, "limit", limit));

        List<Long> recommendedServiceIds;

        switch (algorithm) {
            case "collaborative":
                recommendedServiceIds = collaborativeFilteringService.recommendByUserSimilarity(userId, limit);
                break;
            case "content":
                recommendedServiceIds = contentBasedService.recommendByContent(userId, limit);
                break;
            case "hybrid":
            default:
                recommendedServiceIds = hybridRecommendationService.hybridRecommend(userId, limit);
                break;
        }

        // 获取推荐服务详情
        List<SkillServiceDTO> recommendations = skillServiceService.getServicesByIds(recommendedServiceIds);

        return SaResult.ok("获取推荐成功").setData(recommendations);
    }

    /**
     * 记录推荐点击行为
     */
    @PostMapping("/click")
    @SaCheckLogin
    public SaResult recordRecommendationClick(@RequestBody RecommendationClickRequest request) {
        Integer userId = StpUtil.getLoginIdAsInt();

        // 记录点击行为
        userBehaviorService.recordBehavior(userId, request.getServiceId(), "skill_service", "click",
                Map.of("source", "recommendation", "algorithm", request.getAlgorithm(), "position", request.getPosition()));

        return SaResult.ok("记录成功");
    }
}
```

### 4.3 推荐系统优化策略

#### 4.3.1 冷启动问题解决

```java
@Service
public class ColdStartService {

    /**
     * 新用户推荐
     */
    public List<Long> recommendForNewUser(Integer userId) {
        // 1. 基于用户注册信息推荐
        Users user = userService.getById(userId);
        List<Long> categoryBasedRecommendations = recommendByUserType(user.getUserType());

        // 2. 热门服务推荐
        List<Long> popularRecommendations = popularityService.getPopularServices(10);

        // 3. 混合推荐
        Set<Long> recommendations = new LinkedHashSet<>();
        recommendations.addAll(categoryBasedRecommendations.subList(0, Math.min(5, categoryBasedRecommendations.size())));
        recommendations.addAll(popularRecommendations.subList(0, Math.min(5, popularRecommendations.size())));

        return new ArrayList<>(recommendations);
    }

    /**
     * 新服务推荐
     */
    public void promoteNewService(Long serviceId) {
        // 1. 添加到热门推荐池
        redisTemplate.opsForZSet().add("new_services", serviceId, System.currentTimeMillis());

        // 2. 推送给相关用户
        SkillService service = skillServiceService.getById(serviceId);
        List<Integer> targetUsers = findTargetUsers(service);

        for (Integer userId : targetUsers) {
            // 发送推荐通知
            notificationService.sendRecommendationNotification(userId, serviceId);
        }
    }
}
```

#### 4.3.2 推荐效果评估

```java
@Service
public class RecommendationEvaluationService {

    /**
     * 计算推荐准确率
     */
    public double calculatePrecision(Integer userId, List<Long> recommendations, int days) {
        // 获取用户在指定天数内的实际行为
        List<Long> actualInteractions = getUserActualInteractions(userId, days);

        // 计算推荐命中数
        long hits = recommendations.stream()
                .mapToLong(serviceId -> actualInteractions.contains(serviceId) ? 1 : 0)
                .sum();

        return recommendations.isEmpty() ? 0.0 : (double) hits / recommendations.size();
    }

    /**
     * 计算推荐召回率
     */
    public double calculateRecall(Integer userId, List<Long> recommendations, int days) {
        List<Long> actualInteractions = getUserActualInteractions(userId, days);

        if (actualInteractions.isEmpty()) {
            return 0.0;
        }

        long hits = recommendations.stream()
                .mapToLong(serviceId -> actualInteractions.contains(serviceId) ? 1 : 0)
                .sum();

        return (double) hits / actualInteractions.size();
    }

    /**
     * A/B测试支持
     */
    public String getRecommendationStrategy(Integer userId) {
        // 基于用户ID进行分组
        int group = userId % 100;

        if (group < 50) {
            return "strategy_a"; // 策略A：协同过滤为主
        } else {
            return "strategy_b"; // 策略B：内容推荐为主
        }
    }
}
```

---

*本文档详细分析了项目中的核心技术难点及其解决方案，包括分布式事务处理、并发控制、安全防护和推荐系统等关键技术，为开发团队提供全面的技术参考和实现指导。*
