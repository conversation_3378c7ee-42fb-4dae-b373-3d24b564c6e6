# 校园技能共享平台开发文档

## 1. 项目概述

校园技能共享平台是一个基于Spring Boot + Vue3的前后端分离项目，旨在为校园师生提供技能交流和服务的平台。

### 1.1 项目结构

```
schoolskill/
├── src/                          # 后端源码
│   ├── main/java/com/school/     # Java源码
│   │   ├── controller/           # 控制器层
│   │   ├── service/              # 服务层
│   │   ├── entity/               # 实体类
│   │   ├── mapper/               # 数据访问层
│   │   ├── config/               # 配置类
│   │   └── utils/                # 工具类
│   └── main/resources/           # 资源文件
│       ├── application.yml       # 应用配置
│       └── mapper/               # MyBatis映射文件
├── schoolweb/                    # 前端源码
│   ├── src/                      # Vue3源码
│   │   ├── views/                # 页面组件
│   │   ├── components/           # 公共组件
│   │   ├── service/              # API服务
│   │   ├── store/                # 状态管理
│   │   └── utils/                # 工具函数
│   ├── package.json              # 前端依赖
│   └── vite.config.ts            # Vite配置
├── mysql/                        # 数据库脚本
│   └── school_skill_share.sql    # 数据库初始化脚本
├── docs/                         # 文档目录
├── upload/                       # 文件上传目录
└── pom.xml                       # Maven配置
```

## 2. 环境准备

### 2.1 开发环境要求

#### 开发环境（Windows）
- **Java**: OpenJDK 21 或 Oracle JDK 21
- **Maven**: 3.6.0+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Node.js**: 18.0+
- **IDE**: IntelliJ IDEA 2023+ (统一使用IDEA开发前后端)

### 2.2 环境安装（Windows）

#### 2.2.1 Java环境安装

1. **下载安装JDK 21**
   - 访问 https://adoptium.net/
   - 下载Windows x64版本的OpenJDK 21
   - 运行安装程序，默认安装到 `C:\Program Files\Eclipse Adoptium\jdk-21.x.x.x-hotspot\`

2. **配置环境变量**
   ```
   右键"此电脑" → 属性 → 高级系统设置 → 环境变量

   新建系统变量：
   变量名：JAVA_HOME
   变量值：C:\Program Files\Eclipse Adoptium\jdk-21.x.x.x-hotspot

   编辑Path变量，添加：
   %JAVA_HOME%\bin
   ```

3. **验证安装**
   ```cmd
   java -version
   javac -version
   ```

#### 2.2.2 Maven安装和换源

1. **下载安装Maven**
   - 访问 https://maven.apache.org/download.cgi
   - 下载Binary zip archive版本
   - 解压到 `C:\Program Files\Apache\maven-3.x.x\`

2. **配置环境变量**
   ```
   新建系统变量：
   变量名：MAVEN_HOME
   变量值：C:\Program Files\Apache\maven-3.x.x

   编辑Path变量，添加：
   %MAVEN_HOME%\bin
   ```

3. **Maven换源配置**
   编辑 `%MAVEN_HOME%\conf\settings.xml` 文件，在 `<mirrors>` 标签内添加：
   ```xml
   <mirror>
     <id>aliyunmaven</id>
     <mirrorOf>*</mirrorOf>
     <name>阿里云公共仓库</name>
     <url>https://maven.aliyun.com/repository/public</url>
   </mirror>
   ```

4. **验证安装**
   ```cmd
   mvn -version
   ```

#### 2.2.3 MySQL安装配置

1. **下载安装MySQL 8.0**
   - 访问 https://dev.mysql.com/downloads/mysql/
   - 下载Windows (x86, 64-bit), MSI Installer版本
   - 运行安装程序，选择"Developer Default"安装类型

2. **配置MySQL**
   - 设置root用户密码（建议：root123456）
   - 选择"Use Strong Password Encryption"
   - 配置Windows服务，设置为开机自启

3. **创建项目数据库**
   打开MySQL Command Line Client，执行：
   ```sql
   -- 创建数据库
   CREATE DATABASE school_skill_share CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

   -- 创建用户
   CREATE USER 'school_user'@'localhost' IDENTIFIED BY 'school_password';

   -- 授权
   GRANT ALL PRIVILEGES ON school_skill_share.* TO 'school_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

#### 2.2.4 Redis安装

1. **下载Redis for Windows**
   - 访问 https://github.com/tporadowski/redis/releases
   - 下载最新版本的Redis-x64-x.x.x.msi
   - 运行安装程序，默认安装即可

2. **启动Redis服务**
   - 安装完成后Redis会自动作为Windows服务启动
   - 可通过"服务"管理器查看Redis服务状态
   - 默认端口：6379

#### 2.2.5 Node.js安装和换源

1. **下载安装Node.js**
   - 访问 https://nodejs.org/
   - 下载LTS版本（推荐18.x）
   - 运行安装程序，勾选"Add to PATH"选项

2. **验证安装**
   ```cmd
   node -v
   npm -v
   ```

3. **npm换源配置**
   ```cmd
   # 设置淘宝镜像源
   npm config set registry https://registry.npmmirror.com

   # 验证源设置
   npm config get registry

   # 安装pnpm
   npm install -g pnpm

   # pnpm换源
   pnpm config set registry https://registry.npmmirror.com
   ```

## 3. 项目启动

### 3.1 数据库初始化

1. 确保MySQL服务运行
2. 导入数据库脚本:
```bash
mysql -u school_user -p school_skill_share < mysql/school_skill_share.sql
```

### 3.2 后端启动

#### 3.2.1 配置文件修改

编辑 `src/main/resources/application.yml`:

```yaml
spring:
  datasource:
    url: ******************************************************************************************************************
    username: school_user
    password: school_password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  redis:
    host: localhost
    port: 6379
    password: # Redis密码，如果没有设置则留空
    database: 0
    
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

server:
  port: 8080

# Sa-Token配置
sa-token:
  token-name: satoken
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false
```

#### 3.2.2 启动后端服务

**使用IDE启动:**
1. 导入项目到IntelliJ IDEA
2. 等待Maven依赖下载完成
3. 运行主类 `com.school.CodeAcademyManageSystemApplication`

**使用命令行启动:**
```bash
# 在项目根目录执行
mvn clean compile
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/CodeAcademyManageSystem-1.0.jar
```

**验证后端启动:**
访问 http://localhost:8080/api/test 查看是否返回正常响应

### 3.3 前端启动（使用IntelliJ IDEA）

#### 3.3.1 在IDEA中打开前端项目

1. **打开前端项目**
   - 在IDEA中选择 File → Open
   - 选择项目根目录下的 `schoolweb` 文件夹
   - 等待IDEA识别为Node.js项目

2. **安装依赖**
   在IDEA底部Terminal中执行：
   ```cmd
   pnpm install
   ```

#### 3.3.2 配置环境变量

在 `schoolweb` 目录下创建 `.env.development` 文件：
```env
# 开发环境配置
VITE_APP_TITLE=校园技能共享平台
VITE_APP_BASE_API=http://localhost:8080/api
VITE_APP_UPLOAD_URL=http://localhost:8080/api/file/upload
```

#### 3.3.3 在IDEA中启动前端服务

1. **方法一：使用npm scripts**
   - 打开 `package.json` 文件
   - 在 `scripts` 部分找到 `dev` 脚本
   - 点击脚本旁边的绿色运行按钮

2. **方法二：使用Terminal**
   在IDEA Terminal中执行：
   ```cmd
   pnpm dev
   ```

3. **验证前端启动**
   访问 http://localhost:5173 查看前端页面

## 4. IntelliJ IDEA开发配置

### 4.1 必要插件安装

1. **打开插件管理**
   File → Settings → Plugins

2. **安装以下插件**
   - **Lombok Plugin** - 支持Lombok注解
   - **MyBatis X** - MyBatis增强工具
   - **Vue.js** - Vue.js开发支持
   - **GitToolBox** - Git增强工具
   - **Chinese Language Pack** - 中文语言包（可选）

### 4.2 项目配置

#### 4.2.1 导入项目
1. File → Open → 选择项目根目录
2. 等待Maven依赖下载完成
3. 确认Project SDK设置为JDK 21

#### 4.2.2 代码格式化配置
1. File → Settings → Editor → Code Style
2. 设置Java代码格式：
   - Indent: 4 spaces
   - Continuation indent: 8 spaces
   - Tab size: 4
3. 设置自动格式化：
   - File → Settings → Tools → Actions on Save
   - 勾选 "Reformat code" 和 "Optimize imports"

#### 4.2.3 Maven配置
1. File → Settings → Build → Build Tools → Maven
2. 设置Maven home directory为安装路径
3. 设置User settings file为配置的settings.xml
4. 勾选 "Import Maven projects automatically"

### 4.3 运行配置

#### 4.3.1 后端运行配置
1. 找到主类 `CodeAcademyManageSystemApplication`
2. 右键 → Run 'CodeAcademyManageSystemApplication'
3. 或点击类旁边的绿色运行按钮

#### 4.3.2 前端运行配置
1. 打开 `schoolweb/package.json`
2. 在 scripts 的 dev 脚本旁点击运行按钮
3. 或在Terminal中执行 `pnpm dev`

## 5. 常见问题解决

### 5.1 后端启动问题

**问题1: 数据库连接失败**
```
解决方案:
1. 检查MySQL服务是否启动
2. 验证数据库连接配置
3. 确认数据库用户权限
4. 检查防火墙设置
```

**问题2: Redis连接失败**
```
解决方案:
1. 检查Redis服务是否启动
2. 验证Redis配置
3. 检查Redis密码设置
```

**问题3: 端口占用**
```
解决方案:
1. 查看端口占用: netstat -ano | findstr :8080
2. 杀死占用进程或修改端口配置
```

### 5.2 前端启动问题

**问题1: 依赖安装失败**
```
解决方案:
1. 清除缓存: pnpm store prune
2. 删除node_modules重新安装
3. 检查网络连接和npm源配置
```

**问题2: 编译错误**
```
解决方案:
1. 检查Node.js版本是否符合要求
2. 更新TypeScript和Vue版本
3. 检查代码语法错误
```

### 5.3 跨域问题

**开发环境解决方案:**
在 `schoolweb/vite.config.ts` 中配置代理:
```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      }
    }
  }
})
```

## 6. 开发规范

### 6.1 代码规范

#### 6.1.1 Java代码规范
- 使用驼峰命名法
- 类名首字母大写
- 方法名和变量名首字母小写
- 常量全大写，下划线分隔
- 注释使用JavaDoc格式

#### 6.1.2 Vue代码规范
- 组件名使用PascalCase
- 文件名使用kebab-case
- 变量名使用camelCase
- 常量使用UPPER_SNAKE_CASE
- 使用TypeScript类型注解

### 6.2 Git提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 6.3 分支管理

- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支，用于新功能开发
- `hotfix/*`: 热修复分支，用于紧急修复

---

*本文档提供了完整的项目开发环境搭建和启动指南，帮助开发者快速上手项目开发。*
